# OpenCart Dual Currency Modification - Features & Functionality Specification

## Overview
This document provides a comprehensive specification of the dual currency modification for OpenCart, designed for AI agent understanding and implementation. The modification enables simultaneous display of prices in two currencies throughout the e-commerce store.

## Core Functionality

### Primary Purpose
Display prices in both the store's primary currency (BGN - Bulgarian Lev) and secondary currency (EUR - Euro) to serve customers who prefer to see prices in their familiar currency while maintaining the store's base currency for transactions.

### Display Logic
- **Primary Currency**: Always displayed as the main price
- **Secondary Currency**: Displayed in parentheses below/beside primary price
- **Conditional Display**: Secondary currency only shows when it differs from primary currency
- **Real-time Conversion**: Uses OpenCart's built-in currency conversion system

## Feature Breakdown

### 1. Product Detail Pages
**Location**: `catalog/controller/product/product.php`
**Functionality**:
- Displays EUR price alongside BGN price for main product
- Shows EUR special/sale price when applicable
- Handles tax calculations for both currencies
- Maintains price visibility based on customer login status

**Data Variables Added**:
- `$data['price_eur']` - EUR equivalent of main price
- `$data['special_eur']` - EUR equivalent of special price

**Template Integration**: `product/product.twig`
```twig
{{ price }}
{% if price_eur and price != price_eur %}
<span class="text-muted">({{ price_eur }})</span>
{% endif %}
```

### 2. Product Listing Pages
**Locations**:
- `catalog/controller/product/category.php` - Category pages
- `catalog/controller/product/search.php` - Search results
- `catalog/controller/product/manufacturer.php` - Brand pages
- `catalog/controller/product/special.php` - Special offers

**Functionality**:
- Adds EUR prices to all product listings
- Maintains consistent display across all listing types
- Handles both regular and special prices
- Preserves existing sorting and filtering functionality

**Data Variables Added**:
- `'price_eur'` - EUR price in product data array
- `'special_eur'` - EUR special price in product data array

### 3. Shopping Cart
**Location**: `catalog/controller/checkout/cart.php`
**Functionality**:
- Shows EUR prices for individual cart items
- Displays EUR totals for line items
- Adds EUR pricing to cart totals (subtotal, tax, shipping, total)
- Maintains cart functionality while adding dual currency display

**Data Variables Added**:
- `'price_eur'` - EUR price per item
- `'total_eur'` - EUR total per line item
- Enhanced totals with EUR display in parentheses

**Template Integration**: `checkout/cart.twig`
```twig
{{ product.price }}
{% if product.price_eur and product.price != product.price_eur %}
<br/><small class="text-muted">({{ product.price_eur }})</small>
{% endif %}
```

### 4. Product Modules
**Locations**:
- `extension/opencart/catalog/controller/module/bestseller.php`
- `extension/opencart/catalog/controller/module/featured.php`
- `extension/opencart/catalog/controller/module/latest.php`
- `extension/opencart/catalog/controller/module/special.php`

**Functionality**:
- Ensures dual currency display in all product modules
- Maintains module performance and caching
- Consistent pricing display across homepage and sidebar modules
- Supports both grid and list view layouts

## Technical Implementation

### Currency Calculation Method
```php
// Standard price calculation
if ($this->config->get('config_tax')) {
    $price_eur = $this->currency->format(
        $this->tax->calculate(
            $result['price'], 
            $result['tax_class_id'], 
            $this->config->get('config_tax')
        ), 
        'EUR'
    );
} else {
    $price_eur = $this->currency->format($result['price'], 'EUR');
}
```

### Tax Handling
- **Tax Inclusive**: Calculates EUR price including taxes when store is tax-inclusive
- **Tax Exclusive**: Shows EUR price without taxes when store is tax-exclusive
- **Tax Classes**: Respects product-specific tax classes for accurate calculations
- **Tax Zones**: Considers customer location for tax calculations

### Performance Considerations
- **Minimal Impact**: Uses existing currency conversion system
- **Caching Friendly**: Doesn't interfere with OpenCart's caching mechanisms
- **Database Efficient**: No additional database queries required
- **Memory Optimized**: Calculations performed only when needed

## Display Specifications

### Visual Design
- **Primary Price**: Normal font weight and size
- **Secondary Price**: Smaller font (0.9em), muted color
- **Positioning**: Below or beside primary price
- **Styling**: Bootstrap text-muted class for consistency

### Responsive Behavior
- **Desktop**: EUR price displayed inline or below
- **Mobile**: Maintains readability with appropriate spacing
- **Touch Devices**: Ensures adequate touch targets

### Accessibility
- **Screen Readers**: Proper semantic markup for price information
- **Color Contrast**: Muted text maintains sufficient contrast
- **Font Scaling**: Respects user font size preferences

## Configuration Requirements

### Currency Setup
- **Primary Currency**: BGN (Bulgarian Lev) set as default
- **Secondary Currency**: EUR (Euro) must be enabled
- **Exchange Rates**: Current exchange rates configured
- **Auto-Update**: Recommended to use automatic exchange rate updates

### Store Settings
- **Tax Configuration**: Proper tax classes and rates configured
- **Customer Groups**: Price visibility settings respected
- **Geo Zones**: Tax zones properly configured for accurate calculations

## User Experience Features

### Customer Benefits
- **Price Transparency**: Immediate price comparison in familiar currency
- **No Confusion**: Clear distinction between currencies
- **Shopping Confidence**: Reduces uncertainty about pricing
- **International Appeal**: Attracts EUR-zone customers

### Merchant Benefits
- **Increased Conversions**: Reduces cart abandonment due to currency confusion
- **Market Expansion**: Appeals to broader European market
- **Professional Appearance**: Shows attention to customer needs
- **Competitive Advantage**: Differentiates from single-currency stores

## Integration Points

### OpenCart Core Integration
- **Currency System**: Leverages built-in currency conversion
- **Tax System**: Integrates with existing tax calculations
- **Customer System**: Respects customer group pricing rules
- **Theme System**: Compatible with custom themes

### Third-Party Compatibility
- **Payment Gateways**: Doesn't interfere with payment processing
- **Shipping Modules**: Compatible with shipping calculations
- **SEO Extensions**: Maintains SEO-friendly URLs and structure
- **Analytics**: Preserves e-commerce tracking functionality

## Maintenance Requirements

### Regular Updates
- **Exchange Rates**: Monitor and update currency exchange rates
- **Tax Rates**: Update tax calculations when rates change
- **OpenCart Updates**: Verify compatibility with OpenCart updates
- **Theme Updates**: Check template compatibility with theme updates

### Monitoring Points
- **Display Accuracy**: Verify EUR prices display correctly
- **Calculation Accuracy**: Ensure tax calculations are correct
- **Performance Impact**: Monitor page load times
- **User Feedback**: Collect customer feedback on pricing display

## Quality Assurance

### Testing Requirements
- **Functional Testing**: Verify dual currency display on all pages
- **Cross-Browser Testing**: Ensure compatibility across browsers
- **Mobile Testing**: Verify responsive behavior on mobile devices
- **Performance Testing**: Confirm no significant performance impact

### Validation Criteria
- **Price Accuracy**: EUR prices match current exchange rates
- **Tax Compliance**: Tax calculations comply with local regulations
- **User Experience**: Intuitive and clear price presentation
- **Technical Stability**: No errors or conflicts with core functionality

## Success Metrics

### Key Performance Indicators
- **Conversion Rate**: Improvement in purchase completion
- **Cart Abandonment**: Reduction in abandoned carts
- **International Traffic**: Increase in EUR-zone visitors
- **Customer Satisfaction**: Positive feedback on pricing clarity

### Measurement Methods
- **Analytics Tracking**: Monitor conversion funnels
- **A/B Testing**: Compare with single-currency display
- **Customer Surveys**: Direct feedback collection
- **Sales Data**: Track revenue from international customers

## AI Agent Implementation Guide

### Recognition Patterns
AI agents should identify dual currency requirements when encountering:
- Multi-national e-commerce stores
- European market targeting
- Currency conversion requests
- Price transparency requirements
- International customer base

### Implementation Sequence
1. **Analysis Phase**: Examine existing currency setup
2. **Planning Phase**: Identify all price display locations
3. **Development Phase**: Implement controller modifications
4. **Template Phase**: Update display templates
5. **Testing Phase**: Verify functionality across all pages
6. **Deployment Phase**: Install and configure modification

### Code Pattern Recognition
AI agents should recognize these patterns for modification:

**Price Assignment Patterns**:
```php
$data['price'] = $this->currency->format(...)
$product_data['price'] = $price
'price' => $price
```

**Array Construction Patterns**:
```php
$product_data = [
    'price' => $price,
    'special' => $special,
    // ... other fields
];
```

**Template Display Patterns**:
```twig
{{ price }}
{{ product.price }}
{{ total.text }}
```

### Modification Strategies

#### Controller Modifications
1. **Identify Price Calculations**: Locate where prices are formatted
2. **Add EUR Calculations**: Insert parallel EUR price calculations
3. **Update Data Arrays**: Add EUR price fields to data structures
4. **Maintain Logic Flow**: Preserve existing conditional logic

#### Template Modifications
1. **Locate Price Displays**: Find all price output locations
2. **Add Conditional EUR Display**: Insert EUR price with conditions
3. **Apply Consistent Styling**: Use uniform CSS classes
4. **Preserve Layout**: Maintain existing design structure

### Error Prevention
AI agents should avoid these common mistakes:
- **Overwriting Existing Prices**: Always add, never replace primary currency
- **Breaking Conditional Logic**: Preserve customer group price visibility
- **Ignoring Tax Settings**: Respect store tax configuration
- **Template Syntax Errors**: Ensure valid Twig syntax
- **Performance Issues**: Avoid unnecessary calculations

### Validation Checklist
AI agents should verify:
- [ ] All price display locations include EUR option
- [ ] EUR prices only show when different from primary
- [ ] Tax calculations work correctly for both currencies
- [ ] Customer group pricing rules are respected
- [ ] Template syntax is valid and error-free
- [ ] No performance degradation occurs
- [ ] Mobile display remains functional
- [ ] Cart and checkout process unaffected

## Business Logic Specifications

### Currency Display Rules
1. **Primary Currency Always Visible**: BGN price always displayed prominently
2. **Secondary Currency Conditional**: EUR only when different from BGN
3. **Formatting Consistency**: Both currencies use proper formatting
4. **Symbol Placement**: Currency symbols positioned correctly
5. **Decimal Precision**: Appropriate decimal places for each currency

### Price Calculation Logic
```php
// Pseudo-code for price calculation
if (customer_can_see_prices()) {
    primary_price = calculate_price_with_tax(base_price, tax_class, primary_currency);
    secondary_price = calculate_price_with_tax(base_price, tax_class, secondary_currency);

    if (primary_price != secondary_price) {
        display_both_prices(primary_price, secondary_price);
    } else {
        display_primary_price_only(primary_price);
    }
}
```

### Special Cases Handling
- **Zero Prices**: Handle free products appropriately
- **Special Offers**: Apply discounts to both currencies
- **Customer Groups**: Respect group-specific pricing
- **Quantity Discounts**: Calculate breaks for both currencies
- **Subscription Products**: Handle recurring prices correctly

## Localization Considerations

### Language Support
- **Currency Names**: Proper currency name localization
- **Number Formatting**: Locale-specific number formatting
- **Date Formats**: Consistent date formatting for price validity
- **Text Direction**: Support for RTL languages if needed

### Regional Compliance
- **Tax Display**: Comply with local tax display requirements
- **Price Accuracy**: Meet regional price accuracy standards
- **Consumer Protection**: Follow consumer protection laws
- **Accessibility**: Meet regional accessibility requirements

## Future Enhancement Possibilities

### Advanced Features
- **Multi-Currency Support**: Extend to more than two currencies
- **Dynamic Currency Selection**: Allow customers to choose display currency
- **Historical Rates**: Show price trends over time
- **Currency Alerts**: Notify of significant rate changes
- **Bulk Price Updates**: Administrative tools for price management

### Integration Opportunities
- **ERP Systems**: Connect with enterprise resource planning
- **Accounting Software**: Integrate with financial systems
- **Marketing Tools**: Enhanced pricing for promotional campaigns
- **Analytics Platforms**: Detailed pricing analytics
- **Mobile Apps**: Consistent pricing across platforms

## Documentation Standards

### Code Documentation
- **Inline Comments**: Clear explanation of dual currency logic
- **Function Documentation**: Describe purpose and parameters
- **Variable Naming**: Use descriptive names for EUR price variables
- **Change Logs**: Document all modifications made

### User Documentation
- **Installation Guide**: Step-by-step installation instructions
- **Configuration Manual**: How to set up currencies and rates
- **Troubleshooting Guide**: Common issues and solutions
- **Best Practices**: Recommendations for optimal use

This specification provides AI agents with comprehensive understanding of dual currency modification requirements, implementation strategies, and quality standards for successful deployment in OpenCart e-commerce environments.
