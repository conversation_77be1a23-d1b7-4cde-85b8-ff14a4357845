
<?xml version="1.0" encoding="utf-8"?>
<modification>
    <name>Dual Currency Display (BGN/EUR)</name>
    <code>dual_currency_bgn_eur</code>
    <version>1.0</version>
    <author><PERSON><PERSON><PERSON></author>
    <link>https://www.google.com/gemini</link>
    <documentation><![CDATA[<p>As per the provided specification, this modification enables the simultaneous display of prices in the primary currency (BGN) and a secondary currency (EUR).</p>
    <p><b>Features:</b></p>
    <ul>
        <li>Displays EUR price in parentheses on product pages, category listings, modules, and the shopping cart.</li>
        <li>Handles regular prices, special prices, and taxes correctly.</li>
        <li>Uses OpenCart's built-in currency conversion system for minimal performance impact.</li>
        <li>EUR price is only shown when it differs from the BGN price to avoid redundancy.</li>
    </ul>
    <p><b>Installation:</b></p>
    <ol>
        <li>Go to <b>Extensions > Installer</b> in your OpenCart admin panel.</li>
        <li>Click the <b>Upload</b> button and select this `dual_currency_bgn_eur.ocmod.xml` file.</li>
        <li>Go to <b>Extensions > Modifications</b> and click the blue <b>Refresh</b> button.</li>
    </ol>
    <p><b>Configuration Requirements:</b></p>
    <ul>
        <li>Ensure BGN is your store's default currency.</li>
        <li>Ensure EUR currency is set up and enabled under <b>System > Localisation > Currencies</b>.</li>
        <li>Ensure your currency exchange rates are up to date.</li>
    </ul>]]></documentation>

    <file path="catalog/controller/product/{category,search,manufacturer,special}.php">
        <operation>
            <search><![CDATA[
                    $price = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), $this->session->data['currency']);
            ]]></search>
            <add position="after"><![CDATA[
                    $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
                    $price = $this->currency->format($result['price'], $this->session->data['currency']);
            ]]></search>
            <add position="after"><![CDATA[
                    $price_eur = $this->currency->format($result['price'], 'EUR');
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
                    $special = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), $this->session->data['currency']);
            ]]></search>
            <add position="after"><![CDATA[
                    $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
                    $special = $this->currency->format($result['special'], $this->session->data['currency']);
            ]]></search>
            <add position="after"><![CDATA[
                    $special_eur = $this->currency->format($result['special'], 'EUR');
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
                    'price'       => $price,
            ]]></search>
            <add position="after"><![CDATA[
                    'price_eur'   => isset($price_eur) ? $price_eur : false,
            ]]></add>
        </operation>
         <operation>
            <search><![CDATA[
                    'special'     => $special,
            ]]></search>
            <add position="after"><![CDATA[
                    'special_eur' => isset($special_eur) ? $special_eur : false,
            ]]></add>
        </operation>
    </file>

    <file path="catalog/controller/product/product.php">
        <operation>
            <search><![CDATA[
            if ($this->customer->isLogged() || !$this->config->get('config_customer_price')) {
            ]]></search>
            <add position="before"><![CDATA[
            $data['price_eur'] = '';
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
            $data['price'] = $this->currency->format($this->tax->calculate($product_info['price'], $product_info['tax_class_id'], $this->config->get('config_tax')), $this->session->data['currency']);
            ]]></search>
            <add position="after"><![CDATA[
            $data['price_eur'] = $this->currency->format($this->tax->calculate($product_info['price'], $product_info['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            ]]></add>
        </operation>
         <operation>
            <search><![CDATA[
            if ((float)$product_info['special']) {
            ]]></search>
            <add position="before"><![CDATA[
            $data['special_eur'] = '';
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
            $data['special'] = $this->currency->format($this->tax->calculate($product_info['special'], $product_info['tax_class_id'], $this->config->get('config_tax')), $this->session->data['currency']);
            ]]></search>
            <add position="after"><![CDATA[
            $data['special_eur'] = $this->currency->format($this->tax->calculate($product_info['special'], $product_info['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            ]]></add>
        </operation>
    </file>

    <file path="catalog/view/template/product/product.twig">
        <operation>
            <search index="0"><![CDATA[
                <h2>{{ price }}</h2>
            ]]></search>
            <add position="after"><![CDATA[
                {% if price_eur and price != price_eur %}
                <h3 style="margin-top: 5px;"><span class="text-muted" style="font-size:0.9em;">({{ price_eur }})</span></h3>
                {% endif %}
            ]]></add>
        </operation>
        <operation>
            <search index="0"><![CDATA[
                <h1>{{ special }}</h1>
            ]]></search>
            <add position="after"><![CDATA[
                {% if special_eur and special != special_eur %}
                <h2 style="margin-top: 5px;"><span class="text-muted" style="font-size:0.9em;">({{ special_eur }})</span></h2>
                {% endif %}
            ]]></add>
        </operation>
    </file>

    <file path="extension/opencart/catalog/controller/module/{bestseller,featured,latest,special}.php">
        <operation>
            <search><![CDATA[
                    $price = $this->currency->format($this->tax->calculate($product_info['price'], $product_info['tax_class_id'], $this->config->get('config_tax')), $this->session->data['currency']);
            ]]></search>
            <add position="after"><![CDATA[
                    $price_eur = $this->currency->format($this->tax->calculate($product_info['price'], $product_info['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
                    $price = $this->currency->format($product_info['price'], $this->session->data['currency']);
            ]]></search>
            <add position="after"><![CDATA[
                    $price_eur = $this->currency->format($product_info['price'], 'EUR');
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
                    $special = $this->currency->format($this->tax->calculate($product_info['special'], $product_info['tax_class_id'], $this->config->get('config_tax')), $this->session->data['currency']);
            ]]></search>
            <add position="after"><![CDATA[
                    $special_eur = $this->currency->format($this->tax->calculate($product_info['special'], $product_info['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
                    $special = $this->currency->format($product_info['special'], $this->session->data['currency']);
            ]]></search>
            <add position="after"><![CDATA[
                    $special_eur = $this->currency->format($product_info['special'], 'EUR');
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
                        'price'       => $price,
            ]]></search>
            <add position="after"><![CDATA[
                        'price_eur'   => isset($price_eur) ? $price_eur : false,
            ]]></add>
        </operation>
         <operation>
            <search><![CDATA[
                        'special'     => $special,
            ]]></search>
            <add position="after"><![CDATA[
                        'special_eur' => isset($special_eur) ? $special_eur : false,
            ]]></add>
        </operation>
    </file>
    
    <file path="catalog/view/template/product/thumb.twig">
        <operation>
            <search><![CDATA[
                    <div class="price">{{ price }}</div>
            ]]></search>
            <add position="after"><![CDATA[
                    {% if price_eur and price != price_eur %}<div class="price-eur text-muted" style="font-size:0.9em; margin-top:-5px;">({{ price_eur }})</div>{% endif %}
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
                <span class="price-new">{{ special }}</span> <span class="price-old">{{ price }}</span>
            ]]></search>
            <add position="after"><![CDATA[
                {% if special_eur and special != special_eur %}<div class="price-eur text-muted" style="font-size:0.9em; margin-top:-5px;"><span class="price-new">({{ special_eur }})</span> <span class="price-old">({{ price_eur }})</span></div>{% endif %}
            ]]></add>
        </operation>
    </file>

    <file path="catalog/controller/checkout/cart.php">
        <operation>
            <search><![CDATA[
                        'price'      => $price,
            ]]></search>
            <add position="after"><![CDATA[
                        'price_eur'  => $this->currency->format($this->tax->calculate($product['price'], $product_info['tax_class_id'], $this->config->get('config_tax')), 'EUR'),
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
                        'total'      => $total,
            ]]></search>
            <add position="after"><![CDATA[
                        'total_eur'  => $this->currency->format($product['price'] * $product['quantity'], 'EUR', '', false) > 0 ? $this->currency->format($this->tax->calculate($product['price'], $product_info['tax_class_id'], $this->config->get('config_tax')) * $product['quantity'], 'EUR') : $this->language->get('text_free'),
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
                $data['totals'][] = [
                    'title' => $total['title'],
                    'text'  => $this->currency->format($total['value'], $this->session->data['currency']),
                ];
            ]]></search>
            <add position="replace"><![CDATA[
                $total_eur = $this->currency->format($total['value'], 'EUR');
                $text_bgn = $this->currency->format($total['value'], $this->session->data['currency']);
                $display_text = $text_bgn;
                if ($text_bgn != $total_eur) {
                    $display_text .= ' <span class="text-muted" style="font-size:0.9em;">(' . $total_eur . ')</span>';
                }

                $data['totals'][] = [
                    'title' => $total['title'],
                    'text'  => $display_text,
                ];
            ]]></add>
        </operation>
    </file>
    
    <file path="catalog/view/template/checkout/cart.twig">
        <operation>
            <search><![CDATA[
                  <td class="text-end">{{ product.price }}</td>
            ]]></search>
            <add position="after"><![CDATA[
                  {% if product.price_eur and product.price != product.price_eur %}
                    <td class="text-end text-muted" style="font-size:0.9em;">({{ product.price_eur }})</td>
                  {% else %}
                    <td></td>
                  {% endif %}
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
                  <td class="text-end">{{ product.total }}</td>
            ]]></search>
            <add position="after"><![CDATA[
                  {% if product.total_eur and product.total != product.total_eur %}
                    <td class="text-end text-muted" style="font-size:0.9em;">({{ product.total_eur }})</td>
                  {% else %}
                    <td></td>
                  {% endif %}
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
                <td class="text-end">{{ total.text }}</td>
            ]]></search>
            <add position="replace"><![CDATA[
                <td class="text-end">{{ total.text|raw }}</td>
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[
                <td class="text-end">{{ column_price }}</td>
                <td class="text-end">{{ column_total }}</td>
            ]]></search>
            <add position="replace"><![CDATA[
                <td class="text-end" colspan="2">{{ column_price }}</td>
                <td class="text-end" colspan="2">{{ column_total }}</td>
            ]]></add>
        </operation>
         <operation>
            <search><![CDATA[
                <td class="text-start" colspan="4"><strong>{{ column_name }}</strong></td>
            ]]></search>
            <add position="replace"><![CDATA[
                <td class="text-start" colspan="5"><strong>{{ column_name }}</strong></td>
            ]]></add>
        </operation>
    </file>
</modification>
